/**
 * n8n RSS Filter - Debug Version
 * Simple version with extensive logging to help troubleshoot
 */

// First, let's see what we're getting as input
console.log('=== DEBUG INFO ===');
console.log('Input type:', typeof $input);
console.log('Input all length:', $input.all().length);

// Log first item structure
const firstItem = $input.first();
console.log('First item structure:', JSON.stringify(firstItem, null, 2));

// Try to access the data
try {
  console.log('First item json:', firstItem.json);
  console.log('First item json keys:', Object.keys(firstItem.json || {}));
} catch (e) {
  console.log('Error accessing first item json:', e.message);
}

// Extended blacklist for testing - let's add more terms we can see in your data
const BLACKLIST = [
  "twisted metal",
  "watch what happens",
  "star trek strange new worlds",
  "black tea",
  "alone",
  "jimmy fallon",
  "real time with",
  "the real housewives",
  "bobs burgers",
  "salvage hunters",
  "beat bobby flay",
  "stephen colbert",
  "late night with seth meyers",
  "last week tonight with john oliver"
];

// Normalize function
function normalizeString(str) {
  if (!str) return '';
  return str.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
}

// Simple blacklist check
function isBlacklisted(title) {
  const normalizedTitle = normalizeString(title);
  return BLACKLIST.some(term => normalizedTitle.includes(normalizeString(term)));
}

// Process items
const results = [];
let processed = 0;
let blocked = 0;

for (const item of $input.all()) {
  processed++;
  
  // Log item structure for first few items
  if (processed <= 3) {
    console.log(`Item ${processed} structure:`, JSON.stringify(item, null, 2));
  }
  
  // Try different ways to get the title
  let title = '';
  try {
    title = item.json.title || item.title || '';
  } catch (e) {
    console.log(`Error getting title from item ${processed}:`, e.message);
    continue;
  }
  
  if (!title) {
    console.log(`Item ${processed}: No title found`);
    continue;
  }
  
  console.log(`Item ${processed}: "${title}"`);
  console.log(`  Normalized: "${normalizeString(title)}"`);

  // Check blacklist with detailed logging
  let isBlocked = false;
  const normalizedTitle = normalizeString(title);

  for (const term of BLACKLIST) {
    const normalizedTerm = normalizeString(term);
    if (normalizedTitle.includes(normalizedTerm)) {
      console.log(`  -> BLOCKED by term: "${term}" (normalized: "${normalizedTerm}")`);
      isBlocked = true;
      break;
    }
  }

  if (isBlocked) {
    blocked++;
    continue;
  }

  console.log(`  -> ALLOWED`);
  
  // Add to results
  results.push({
    json: {
      title: title,
      pubDate: item.json.pubDate || item.pubDate || '',
      content: item.json.content || item.json.contentSnippet || item.content || ''
    }
  });
}

console.log(`=== SUMMARY ===`);
console.log(`Processed: ${processed} items`);
console.log(`Blocked: ${blocked} items`);
console.log(`Allowed: ${results.length} items`);

return results;
