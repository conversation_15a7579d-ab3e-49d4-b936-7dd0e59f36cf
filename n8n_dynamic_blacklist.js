/**
 * n8n RSS Filter - Dynamic Blacklist Version
 * Gets blacklist from input using n8n syntax: $input.first().json.blacklist
 */

// Normalize string function
function normalizeString(str) {
  if (!str) return '';
  return str.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
}

// Check if title is blacklisted
function isBlacklisted(title, blacklist) {
  if (!title || !blacklist) return false;
  const normalizedTitle = normalizeString(title);
  return blacklist.some(term => normalizedTitle.includes(normalizeString(term)));
}

// Get blacklist from input
let blacklist = [];
try {
  blacklist = $input.first().json.blacklist;
  console.log(`Loaded ${blacklist.length} blacklist terms from input`);
} catch (e) {
  console.error('Could not load blacklist from input:', e.message);
  return [];
}

// Process RSS items (skip first item if it contains blacklist)
const allItems = $input.all();
const rssItems = allItems.slice(1); // Skip first item (blacklist)

const filteredItems = [];
let blockedCount = 0;

// Process each RSS item
for (const item of rssItems) {
  const title = item.json.title || '';
  
  if (!title) {
    console.log('Skipping item without title');
    continue;
  }
  
  // Check if blacklisted
  if (isBlacklisted(title, blacklist)) {
    console.log(`Filtered out: "${title}"`);
    blockedCount++;
    continue;
  }
  
  // Add to filtered results
  filteredItems.push({
    json: {
      title: item.json.title || '',
      pubDate: item.json.pubDate || '',
      content: item.json.content || item.json.contentSnippet || ''
    }
  });
}

console.log(`Filtering complete: ${rssItems.length} RSS items -> ${filteredItems.length} items (${blockedCount} blocked)`);

// Return filtered items
return filteredItems;
