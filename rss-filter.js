/**
 * RSS Feed Filter Script
 * Filters RSS feed items based on a blacklist
 * Normalizes titles by removing special characters for better matching
 */

/**
 * Normalizes a string by removing special characters and converting to lowercase
 * @param {string} str - The string to normalize
 * @returns {string} - The normalized string
 */
function normalizeString(str) {
    if (!str) return '';
    
    return str
        .toLowerCase()
        .replace(/[^\w\s]/g, ' ')  // Replace special characters with spaces
        .replace(/\s+/g, ' ')      // Replace multiple spaces with single space
        .trim();
}

/**
 * Checks if a title matches any blacklist term
 * @param {string} title - The title to check
 * @param {string[]} blacklist - Array of blacklisted terms
 * @returns {boolean} - True if title matches blacklist, false otherwise
 */
function isBlacklisted(title, blacklist) {
    if (!title || !blacklist || !Array.isArray(blacklist)) {
        return false;
    }
    
    const normalizedTitle = normalizeString(title);
    
    return blacklist.some(blacklistTerm => {
        const normalizedBlacklistTerm = normalizeString(blacklistTerm);
        return normalizedTitle.includes(normalizedBlacklistTerm);
    });
}

/**
 * Filters RSS feed items based on blacklist
 * @param {Object[]} rssItems - Array of RSS feed items
 * @param {Object} blacklistConfig - Blacklist configuration object
 * @returns {Object[]} - Filtered RSS items
 */
function filterRSSFeed(rssItems, blacklistConfig) {
    if (!rssItems || !Array.isArray(rssItems)) {
        console.error('Invalid RSS items provided');
        return [];
    }
    
    if (!blacklistConfig || !blacklistConfig.blacklist || !Array.isArray(blacklistConfig.blacklist)) {
        console.warn('No valid blacklist provided, returning all items');
        return rssItems;
    }
    
    const blacklist = blacklistConfig.blacklist;
    
    return rssItems.filter(item => {
        if (!item.title) {
            console.warn('RSS item missing title:', item);
            return true; // Keep items without titles
        }
        
        const isBlocked = isBlacklisted(item.title, blacklist);
        
        if (isBlocked) {
            console.log(`Filtered out: "${item.title}"`);
        }
        
        return !isBlocked;
    });
}

/**
 * Extracts relevant fields from RSS items for n8n workflow
 * @param {Object[]} rssItems - Array of RSS feed items
 * @returns {Object[]} - Array with only title, pubDate, and content fields
 */
function extractRelevantFields(rssItems) {
    return rssItems.map(item => ({
        title: item.title || '',
        pubDate: item.pubDate || '',
        content: item.content || item.contentSnippet || ''
    }));
}

/**
 * Main function to process RSS feed with blacklist filtering
 * @param {Object[]} rssItems - Array of RSS feed items
 * @param {Object} blacklistConfig - Blacklist configuration
 * @returns {Object[]} - Filtered RSS items with relevant fields
 */
function processRSSFeed(rssItems, blacklistConfig) {
    try {
        // Filter out blacklisted items
        const filteredItems = filterRSSFeed(rssItems, blacklistConfig);
        
        // Extract only relevant fields for n8n
        const processedItems = extractRelevantFields(filteredItems);
        
        console.log(`Processed ${rssItems.length} items, ${processedItems.length} items passed filter`);
        
        return processedItems;
    } catch (error) {
        console.error('Error processing RSS feed:', error);
        return [];
    }
}

// Example usage for testing
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = {
        normalizeString,
        isBlacklisted,
        filterRSSFeed,
        extractRelevantFields,
        processRSSFeed
    };
} else {
    // Browser environment or n8n
    // Functions are available globally
}

// Example test data
const exampleBlacklist = {
    "blacklist": [
        "black tea",
        "ebony and ivory", 
        "the occupant",
        "chief of war",
        "boksen danmark"
    ]
};

const exampleRSSItems = [
    {
        "creator": "Anonym Uploader",
        "title": "Lilo.and.Stich.2025.NORDiC.1080p.BluRay.x264.DTS-HD.MA7.1-TWA",
        "link": "https://danishbytes.club/torrent/150070",
        "pubDate": "Mon, 11 Aug 2025 12:39:01 +0200",
        "content": "Name: Lilo.and.Stich.2025.NORDiC.1080p.BluRay.x264.DTS-HD.MA7.1-TWA..."
    },
    {
        "title": "Transplant S04E10 720p WEB H264-SYLiX",
        "link": "https://ipt.beelyrics.net/t/6818433",
        "pubDate": "Mon, 11 Aug 2025 11:01:51 +0000",
        "content": "1.56 GB; TV/Web-DL"
    },
    {
        "title": "Black.Tea.Documentary.2025.1080p.WEB-DL",
        "pubDate": "Mon, 11 Aug 2025 10:00:00 +0000",
        "content": "Documentary about tea"
    }
];

// Test the filtering (uncomment to run)
// console.log('Testing RSS filter...');
// const result = processRSSFeed(exampleRSSItems, exampleBlacklist);
// console.log('Filtered results:', result);
