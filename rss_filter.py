#!/usr/bin/env python3
"""
RSS Feed Filter Script
Filters RSS feed items based on a blacklist by normalizing titles
Processes JSON files: blacklist.json, dby.json, ipt.json
"""

import json
import re
import sys
from typing import List, Dict, Any


def normalize_string(text: str) -> str:
    """
    Normalizes a string by removing special characters and converting to lowercase
    
    Args:
        text: The string to normalize
        
    Returns:
        The normalized string
    """
    if not text:
        return ''
    
    # Convert to lowercase and replace special characters with spaces
    normalized = re.sub(r'[^\w\s]', ' ', text.lower())
    # Replace multiple spaces with single space
    normalized = re.sub(r'\s+', ' ', normalized)
    
    return normalized.strip()


def is_blacklisted(title: str, blacklist: List[str]) -> bool:
    """
    Checks if a title matches any blacklist term
    
    Args:
        title: The title to check
        blacklist: List of blacklisted terms
        
    Returns:
        True if title matches blacklist, False otherwise
    """
    if not title or not blacklist:
        return False
    
    normalized_title = normalize_string(title)
    
    for blacklist_term in blacklist:
        normalized_blacklist_term = normalize_string(blacklist_term)
        if normalized_blacklist_term in normalized_title:
            return True
    
    return False


def extract_relevant_fields(item: Dict[str, Any]) -> Dict[str, str]:
    """
    Extracts title, pubDate, and content from RSS item
    
    Args:
        item: RSS feed item dictionary
        
    Returns:
        Dictionary with title, pubDate, and content
    """
    return {
        'title': item.get('title', ''),
        'pubDate': item.get('pubDate', ''),
        'content': item.get('content', item.get('contentSnippet', ''))
    }


def filter_rss_feed(rss_items: List[Dict[str, Any]], blacklist: List[str]) -> List[Dict[str, str]]:
    """
    Filters RSS feed items based on blacklist
    
    Args:
        rss_items: List of RSS feed items
        blacklist: List of blacklisted terms
        
    Returns:
        List of filtered RSS items with relevant fields
    """
    if not rss_items:
        return []
    
    filtered_items = []
    
    for item in rss_items:
        title = item.get('title', '')
        
        if not title:
            print(f"Warning: RSS item missing title: {item}")
            continue
        
        if is_blacklisted(title, blacklist):
            print(f"Filtered out: '{title}'")
            continue
        
        # Extract relevant fields
        filtered_item = extract_relevant_fields(item)
        filtered_items.append(filtered_item)
    
    return filtered_items


def load_json_file(filename: str) -> Any:
    """
    Loads JSON data from file
    
    Args:
        filename: Path to JSON file
        
    Returns:
        Parsed JSON data
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found")
        return None
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in '{filename}': {e}")
        return None


def save_json_file(data: Any, filename: str) -> bool:
    """
    Saves data to JSON file
    
    Args:
        data: Data to save
        filename: Output filename
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error saving '{filename}': {e}")
        return False


def main():
    """
    Main function to process RSS feeds with blacklist filtering
    """
    # Load blacklist
    blacklist_data = load_json_file('blacklist.json')
    if not blacklist_data:
        return
    
    # Extract blacklist terms (handle both array and object format)
    if isinstance(blacklist_data, list) and len(blacklist_data) > 0:
        blacklist = blacklist_data[0].get('blacklist', [])
    else:
        blacklist = blacklist_data.get('blacklist', [])
    
    if not blacklist:
        print("Error: No blacklist found in blacklist.json")
        return
    
    print(f"Loaded {len(blacklist)} blacklist terms")
    
    # Process DBY feed
    dby_data = load_json_file('dby.json')
    if dby_data:
        print(f"\nProcessing DBY feed with {len(dby_data)} items...")
        filtered_dby = filter_rss_feed(dby_data, blacklist)
        print(f"DBY: {len(dby_data)} items -> {len(filtered_dby)} items after filtering")
        
        if save_json_file(filtered_dby, 'dby_filtered.json'):
            print("Saved filtered DBY results to 'dby_filtered.json'")
    
    # Process IPT feed
    ipt_data = load_json_file('ipt.json')
    if ipt_data:
        print(f"\nProcessing IPT feed with {len(ipt_data)} items...")
        filtered_ipt = filter_rss_feed(ipt_data, blacklist)
        print(f"IPT: {len(ipt_data)} items -> {len(filtered_ipt)} items after filtering")
        
        if save_json_file(filtered_ipt, 'ipt_filtered.json'):
            print("Saved filtered IPT results to 'ipt_filtered.json'")
    
    # Combine both feeds if both exist
    if dby_data and ipt_data:
        combined_filtered = filtered_dby + filtered_ipt
        print(f"\nCombined filtered results: {len(combined_filtered)} items")
        
        if save_json_file(combined_filtered, 'combined_filtered.json'):
            print("Saved combined filtered results to 'combined_filtered.json'")


if __name__ == "__main__":
    main()
