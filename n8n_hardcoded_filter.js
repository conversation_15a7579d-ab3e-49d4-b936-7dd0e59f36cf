/**
 * n8n RSS Filter - Hardcoded Blacklist Version
 * Complete integration with your existing code
 */

// Hardcoded blacklist (update this when needed)
const BLACKLIST = [
  "black tea", "ebony and ivory", "the occupant", "chief of war", "boksen danmark",
  "hva bruden ikke", "the grim adventures", "real time with", "beliggenhed beliggenhed beliggenhed",
  "back to the", "georgie and mandys", "love after lockup", "necaxa", "the real housewives",
  "alone", "jimmy fallon", "watch what happens", "salvage hunters", "the long road",
  "sports gone wrong", "tyler perrys sistas", "beat bobby flay", "beyond the gates",
  "dateline secrets uncovered", "stephen colbert", "bobs burgers", "icrime with elizabeth",
  "resident alien", "love island beyond", "revival", "twisted metal",
  "welcome to the outcasts restaurant", "late night with seth meyers", "bring her back",
  "desert dawn", "june and john", "how to train your dragon", "a brooklyn love story",
  "conjuring tapes", "star trek strange new worlds", "15 days", "toxic", "smoke",
  "hva bruden ikke ved", "the grim adventures of billy and mandy", "last week tonight with john oliver",
  "real time with bill maher", "hudson og rex", "watari kuns is about to collapse",
  "secrets of the silent witch", "outlander blood of my blood", "drag race brasil", "furia",
  "love island beyond the villa", "the real housewives of orange county", "good luck",
  "the winning try", "the great australian bake off", "cirkus summarum"
];

console.log(`Using blacklist with ${BLACKLIST.length} terms`);

// Helper functions
function extractYearFromTitle(title) {
  const match = title.match(/[^0-9](19|20)\d{2}[^0-9]/);
  return match ? parseInt(match[0].match(/\d{4}/)[0]) : null;
}

function extractIMDB(content) {
  const match = content.match(/IMDB Link:<a href="https?:\/\/anon\.to\?https?:\/\/www\.imdb\.com\/title\/(tt\d+)"[^>]*>/i);
  if (match) {
    return `https://www.imdb.com/title/${match[1]}`;
  }
  const directMatch = content.match(/IMDB Link:<a href="(https?:\/\/www\.imdb\.com\/title\/tt\d+)"[^>]*>/i);
  if (directMatch) return directMatch[1];
  return null;
}

// Your existing extractBaseTitle function
function extractBaseTitle(title) {
  const stopWords = [
    '1080p', '720p', '2160p', 'web-dl', 'webrip', 'bluray', 'hdrip', 'dvdrip',
    'x264', 'x265', 'h.264', 'h.265', 'hevc', 'av1', 'ddp5.1', 'ddp', 'aac',
    '10bit', '8bit', 'amzn', 'nf', 'dsnp', 'flux', 'ivy', 'edith', 'repack',
    'proper', 'multisub', 'nordic', 'dk', 'se', 'no', 'fi', 'twa', 'twaseries',
    'megusta', 'internal', 'extended', 'sdr', 'truehd', 'atmos', 'imax', 'uhd',
    'dv', 'hdr10+', 'hdr', 'remux', '5 1-lama', 'lama', 'd3us', 'dts-hdma5 1',
    'avc-d3g', 'webrip-lama', 'x264-d3us', 'hdr10', 'dd2.0', 'vision', 'showtime',
    'danish', 'english', 'swedish', 'norwegian', 'finnish', 'german', 'french',
    'spanish', 'italian', 'aac2.0'
  ];

  const cleaned = title.replace(/\(.*?\)/g, '').replace(/[^a-zA-Z0-9\s]/g, ' ').replace(/\s+/g, ' ').trim();
  const parts = cleaned.split(/\s+/);
  let result = [];

  for (const part of parts) {
    const lower = part.toLowerCase();
    if (
      stopWords.includes(lower) ||
      /^s\d{1,2}e\d{1,2}$/i.test(part) ||
      /^s\d{1,2}$/i.test(part) ||
      /^s\d{1,2}\s*s\d{1,2}$/i.test(part.replace(/\s+/g, ' '))
    ) {
      break;
    }
    if (/^(19|20)\d{2}$/.test(part)) {
      continue;
    }
    result.push(part);
  }

  return result.join(' ').toLowerCase().trim() || 'unknown';
}

// Check if base title is blacklisted
function isBlacklisted(title) {
  const baseTitle = extractBaseTitle(title);
  return BLACKLIST.some(term => {
    const normalizedTerm = term.toLowerCase().trim();
    return baseTitle.includes(normalizedTerm);
  });
}

// Get all RSS items and filter them
const items = $input.all();
console.log(`Starting with ${items.length} RSS items`);

const filteredItems = [];
let blockedCount = 0;

for (const item of items) {
  const title = item.json.title || '';
  
  if (!title) {
    continue;
  }
  
  // Check if blacklisted
  if (isBlacklisted(title)) {
    const baseTitle = extractBaseTitle(title);
    console.log(`BLOCKED: "${title}" -> base: "${baseTitle}"`);
    blockedCount++;
    continue;
  }
  
  // Keep the item
  filteredItems.push(item);
}

console.log(`Blacklist filtering: ${items.length} -> ${filteredItems.length} items (${blockedCount} blocked)`);

// Continue with your existing processing logic using filteredItems
const thisYear = new Date().getFullYear();
const allowedYears = [thisYear, thisYear - 1];

const seenTitles = new Set();
const groups = { Movie: [], TV: [] };

for (const item of filteredItems) {
  const { title, pubDate, content, link } = item.json;
  const site = link.includes('danishbytes') ? 'dby' : 'ipt';

  const date = new Date(pubDate);
  const formattedDate = date.toLocaleString('da-DK', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  });

  const year = extractYearFromTitle(title);
  if (year && !allowedYears.includes(year)) continue;

  const baseTitle = extractBaseTitle(title);
  if (seenTitles.has(baseTitle)) continue;
  seenTitles.add(baseTitle);

  let info = '';
  let category = 'Other';
  let imdb = null;

  if (site === 'dby') {
    const type = content.match(/<strong>Type<\/strong>:\s*(.*?)<br>/)?.[1] || '-';
    const size = content.match(/<strong>Size<\/strong>:\s*(.*?)<br>/)?.[1] || '-';
    info = `📦 ${type} – 💾 ${size}`;
    category = content.match(/<strong>Category<\/strong>:\s*(.*?)<br>/)?.[1] || 'Other';
    imdb = extractIMDB(content);
  } else {
    info = content;
    category = /TV/i.test(content) ? 'TV' : /Movie|Film/i.test(content) ? 'Movie' : 'Other';
  }

  const imdbHtml = imdb ? ` &nbsp;|&nbsp; 🎬 <a href="${imdb}" style="color:#444;">IMDB</a>` : '';
  const infoHtml = site === 'dby' ? `${info}${imdbHtml}` : info;

  const encodedTitle = encodeURIComponent(baseTitle);
  const webhookUrl = `https://n8n.duksekkan.nohost.me/n8n/webhook/1185f65c-9755-45bc-b60c-b42b80760894/:torrentID=${encodedTitle}`;

  const block = `
  <div style="margin-bottom: 10px; padding-bottom: 8px; border-bottom: 1px solid #ccc;">
    <strong>🎞️ Titel:</strong> ${title}<br>
    <strong>🌍 Site:</strong> ${site.toUpperCase()}<br>
    <strong>🧾 Info:</strong> ${infoHtml}<br>
    <strong>🕒 Date:</strong> ${formattedDate}<br>
    <a href="${webhookUrl}" style="display: inline-block; padding: 8px 12px; margin-top: 8px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 4px;">Udeluk torrent</a>
  </div>`;

  if (/Movie|Film/i.test(category)) groups.Movie.push(block);
  else if (/TV/i.test(category)) groups.TV.push(block);
}

let html = `
  <div style="font-family: Arial, sans-serif; color: #222;">
    <h2 style="margin-bottom: 4px;">🍿 Ugens Torrents</h2>
    <div style="font-size: 13px; color: #666;">Totalt antal: ${groups.Movie.length + groups.TV.length} (${blockedCount} filtreret ud)</div>
    <br>
`;

if (groups.Movie.length) {
  html += `<h3 style="border-bottom: 2px solid #ccc; padding-bottom: 4px;">🎬 Film</h3>`;
  html += groups.Movie.join('');
}

if (groups.TV.length) {
  html += `<h3 style="border-bottom: 2px solid #ccc; padding-top: 20px; padding-bottom: 4px;">📺 TV Shows</h3>`;
  html += groups.TV.join('');
}

html += `</div>`;

return [{ json: { html } }];
