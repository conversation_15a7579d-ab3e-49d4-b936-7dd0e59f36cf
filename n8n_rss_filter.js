/**
 * n8n RSS Feed Filter
 * To be used in n8n Code node
 * Filters RSS feed items based on blacklist
 */

// Blacklist configuration - update this array with your blacklist terms
const BLACKLIST = [
  "black tea",
  "ebony and ivory",
  "the occupant",
  "chief of war",
  "boksen danmark",
  "hva bruden ikke",
  "the grim adventures",
  "real time with",
  "beliggenhed beliggenhed beliggenhed",
  "back to the",
  "georgie and mandys",
  "love after lockup",
  "necaxa",
  "the real housewives",
  "alone",
  "jimmy fallon",
  "watch what happens",
  "salvage hunters",
  "the long road",
  "sports gone wrong",
  "tyler perrys sistas",
  "beat bobby flay",
  "beyond the gates",
  "dateline secrets uncovered",
  "stephen colbert",
  "bobs burgers",
  "icrime with elizabeth",
  "resident alien",
  "love island beyond",
  "revival",
  "twisted metal",
  "welcome to the outcasts restaurant",
  "late night with seth meyers",
  "bring her back",
  "desert dawn",
  "june and john",
  "how to train your dragon",
  "a brooklyn love story",
  "conjuring tapes",
  "star trek strange new worlds",
  "15 days",
  "toxic",
  "smoke",
  "hva bruden ikke ved",
  "the grim adventures of billy and mandy",
  "last week tonight with john oliver",
  "real time with bill maher",
  "hudson og rex",
  "watari kuns is about to collapse",
  "secrets of the silent witch",
  "outlander blood of my blood",
  "drag race brasil",
  "furia",
  "love island beyond the villa",
  "the real housewives of orange county",
  "good luck",
  "the winning try",
  "the great australian bake off",
  "cirkus summarum"
];

/**
 * Normalizes a string by removing special characters and converting to lowercase
 */
function normalizeString(str) {
  if (!str) return '';
  
  return str
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')  // Replace special characters with spaces
    .replace(/\s+/g, ' ')      // Replace multiple spaces with single space
    .trim();
}

/**
 * Checks if a title matches any blacklist term
 */
function isBlacklisted(title, blacklist) {
  if (!title || !blacklist || !Array.isArray(blacklist)) {
    return false;
  }
  
  const normalizedTitle = normalizeString(title);
  
  return blacklist.some(blacklistTerm => {
    const normalizedBlacklistTerm = normalizeString(blacklistTerm);
    return normalizedTitle.includes(normalizedBlacklistTerm);
  });
}

/**
 * Extracts relevant fields from RSS item
 */
function extractRelevantFields(item) {
  return {
    title: item.title || '',
    pubDate: item.pubDate || '',
    content: item.content || item.contentSnippet || ''
  };
}

// Main n8n code execution
try {
  // Get all input items from previous node
  const inputItems = $input.all();
  
  if (!inputItems || inputItems.length === 0) {
    console.log('No input items received');
    return [];
  }
  
  console.log(`Processing ${inputItems.length} RSS items...`);
  
  const filteredItems = [];
  let blockedCount = 0;
  
  for (const inputItem of inputItems) {
    // Extract the RSS item data (adjust this based on your n8n data structure)
    const rssItem = inputItem.json || inputItem;
    
    const title = rssItem.title || '';
    
    if (!title) {
      console.log('Warning: RSS item missing title:', rssItem);
      continue;
    }
    
    // Check if title is blacklisted
    if (isBlacklisted(title, BLACKLIST)) {
      console.log(`Filtered out: "${title}"`);
      blockedCount++;
      continue;
    }
    
    // Extract relevant fields and add to filtered results
    const filteredItem = extractRelevantFields(rssItem);
    filteredItems.push({ json: filteredItem });
  }
  
  console.log(`Filtering complete: ${inputItems.length} items -> ${filteredItems.length} items (${blockedCount} blocked)`);
  
  // Return filtered items for next n8n node
  return filteredItems;
  
} catch (error) {
  console.error('Error in RSS filter:', error);
  throw error;
}
