#!/usr/bin/env python3
"""
Test script for RSS filter functionality
"""

from rss_filter import normalize_string, is_blacklisted, filter_rss_feed, extract_relevant_fields
import json


def test_normalize_string():
    """Test the normalize_string function"""
    print("Testing normalize_string function:")
    
    test_cases = [
        ("Lilo.and.Stich.2025.NORDiC.1080p.BluRay.x264.DTS-HD.MA7.1-TWA", "lilo and stich 2025 nordic 1080p bluray x264 dts hd ma7 1 twa"),
        ("Twisted Metal S02E03", "twisted metal s02e03"),
        ("Watch What Happens Live 2025", "watch what happens live 2025"),
        ("Black.Tea.Documentary", "black tea documentary"),
        ("The-Occupant-2020", "the occupant 2020")
    ]
    
    for original, expected in test_cases:
        result = normalize_string(original)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{original}' -> '{result}'")
        if result != expected:
            print(f"    Expected: '{expected}'")


def test_blacklist_matching():
    """Test blacklist matching functionality"""
    print("\nTesting blacklist matching:")
    
    blacklist = [
        "black tea",
        "twisted metal", 
        "watch what happens",
        "the occupant",
        "star trek strange new worlds"
    ]
    
    test_cases = [
        ("Lilo.and.Stich.2025.NORDiC.1080p.BluRay.x264.DTS-HD.MA7.1-TWA", False),
        ("Twisted Metal S02E03 iNTERNAL MULTi 1080p WEB H264-LUCKY", True),
        ("Watch What Happens Live 2025 08 10 1080p WEB h264-EDITH", True),
        ("Black.Tea.Documentary.2025.1080p.WEB-DL", True),
        ("Star.Trek.Strange.New.Worlds.S03E03.NORDiC.1080p.WEB-DL.H.264.DDP5.1-TWASERiES", True),
        ("Monster Trucks 2016 1080p AMZN WEB-DL DDP 5 1 H 264-PiRaTeS", False)
    ]
    
    for title, should_be_blacklisted in test_cases:
        result = is_blacklisted(title, blacklist)
        status = "✓" if result == should_be_blacklisted else "✗"
        action = "BLOCKED" if result else "ALLOWED"
        print(f"  {status} '{title}' -> {action}")


def test_with_real_data():
    """Test with actual data from JSON files"""
    print("\nTesting with real data:")
    
    # Load blacklist
    try:
        with open('blacklist.json', 'r', encoding='utf-8') as f:
            blacklist_data = json.load(f)
        blacklist = blacklist_data[0]['blacklist']
        print(f"Loaded {len(blacklist)} blacklist terms")
    except Exception as e:
        print(f"Error loading blacklist: {e}")
        return
    
    # Test with a few IPT items
    try:
        with open('ipt.json', 'r', encoding='utf-8') as f:
            ipt_data = json.load(f)
        
        print(f"\nTesting first 10 IPT items:")
        test_items = ipt_data[:10]
        
        for item in test_items:
            title = item.get('title', '')
            is_blocked = is_blacklisted(title, blacklist)
            status = "BLOCKED" if is_blocked else "ALLOWED"
            print(f"  {status}: {title}")
            
    except Exception as e:
        print(f"Error loading IPT data: {e}")


if __name__ == "__main__":
    test_normalize_string()
    test_blacklist_matching()
    test_with_real_data()
