/**
 * Simple test to see input structure in n8n
 */

// Log everything about the input
console.log('=== INPUT DEBUG ===');
console.log('Total inputs:', $input.all().length);

// Check first item
const firstItem = $input.first();
console.log('First item keys:', Object.keys(firstItem));
console.log('First item json keys:', Object.keys(firstItem.json || {}));

// Try to access blacklist
try {
  const blacklist = $input.first().json.blacklist;
  console.log('Blacklist found:', Array.isArray(blacklist));
  console.log('Blacklist length:', blacklist ? blacklist.length : 'null');
  console.log('First 3 blacklist terms:', blacklist ? blacklist.slice(0, 3) : 'none');
} catch (e) {
  console.log('Error accessing blacklist:', e.message);
}

// Check structure of RSS items
console.log('\n=== RSS ITEMS ===');
const allItems = $input.all();
for (let i = 0; i < Math.min(3, allItems.length); i++) {
  console.log(`Item ${i} keys:`, Object.keys(allItems[i].json || {}));
  console.log(`Item ${i} title:`, allItems[i].json?.title || 'NO TITLE');
}

// Just return first few items for now
return $input.all().slice(0, 5);
