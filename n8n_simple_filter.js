/**
 * n8n RSS Filter - Simple Version
 * Optimized for n8n Code node with correct variable syntax
 */

// Blacklist terms
const BLACKLIST = [
  "black tea", "ebony and ivory", "the occupant", "chief of war", "boksen danmark",
  "hva bruden ikke", "the grim adventures", "real time with", "beliggenhed beliggenhed beliggenhed",
  "back to the", "georgie and mandys", "love after lockup", "necaxa", "the real housewives",
  "alone", "jimmy fallon", "watch what happens", "salvage hunters", "the long road",
  "sports gone wrong", "tyler perrys sistas", "beat bobby flay", "beyond the gates",
  "dateline secrets uncovered", "stephen colbert", "bobs burgers", "icrime with elizabeth",
  "resident alien", "love island beyond", "revival", "twisted metal",
  "welcome to the outcasts restaurant", "late night with seth meyers", "bring her back",
  "desert dawn", "june and john", "how to train your dragon", "a brooklyn love story",
  "conjuring tapes", "star trek strange new worlds", "15 days", "toxic", "smoke",
  "hva bruden ikke ved", "the grim adventures of billy and mandy", "last week tonight with john oliver",
  "real time with bill maher", "hudson og rex", "watari kuns is about to collapse",
  "secrets of the silent witch", "outlander blood of my blood", "drag race brasil", "furia",
  "love island beyond the villa", "the real housewives of orange county", "good luck",
  "the winning try", "the great australian bake off", "cirkus summarum"
];

// Normalize string function
function normalizeString(str) {
  if (!str) return '';
  return str.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
}

// Check if title is blacklisted
function isBlacklisted(title) {
  const normalizedTitle = normalizeString(title);
  return BLACKLIST.some(term => normalizedTitle.includes(normalizeString(term)));
}

// Main filtering logic
const filteredItems = [];
let blockedCount = 0;

// Process all input items
for (const item of $input.all()) {
  const title = item.json.title || '';
  
  if (!title) {
    console.log('Skipping item without title');
    continue;
  }
  
  // Check if blacklisted
  if (isBlacklisted(title)) {
    console.log(`Filtered out: "${title}"`);
    blockedCount++;
    continue;
  }
  
  // Add to filtered results with only needed fields
  filteredItems.push({
    json: {
      title: item.json.title || '',
      pubDate: item.json.pubDate || '',
      content: item.json.content || item.json.contentSnippet || ''
    }
  });
}

console.log(`Filtering complete: ${$input.all().length} items -> ${filteredItems.length} items (${blockedCount} blocked)`);

// Return filtered items
return filteredItems;
