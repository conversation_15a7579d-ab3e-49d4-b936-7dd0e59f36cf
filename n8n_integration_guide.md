# n8n RSS Filter Integration Guide

## Filer oprettet:
1. `n8n_rss_filter.js` - Simpel version med hardcoded blacklist
2. `n8n_rss_filter_flexible.js` - Fleksibel version der kan få blacklist fra andre nodes

## Integration i n8n

### Metode 1: Simpel Code Node (Anbefalet)

1. **Tilføj en Code node** i dit n8n workflow
2. **Kopiér indholdet** fra `n8n_rss_filter.js` ind i Code noden
3. **Forbind din RSS node** til Code noden som input
4. **Konfigurér output** til næste node i dit flow

### Metode 2: Fleksibel med separat blacklist

1. **Tilføj en HTTP Request node** eller **Read Binary File node** til at hente `blacklist.json`
2. **Tilføj en Code node** med indholdet fra `n8n_rss_filter_flexible.js`
3. **Forbind både RSS feed og blacklist** til Code noden

### Input format forventet:

Code noden forventer RSS items i dette format:
```json
{
  "title": "Titel på RSS item",
  "pubDate": "Dato for publikation", 
  "content": "Indhold eller contentSnippet"
}
```

### Output format:

Code noden returnerer filtrerede items med kun de relevante felter:
```json
{
  "title": "Titel på RSS item",
  "pubDate": "Dato for publikation",
  "content": "Indhold"
}
```

### Workflow eksempel:

```
RSS Feed Node → Code Node (Filter) → Næste processing node
```

eller

```
RSS Feed Node ↘
               → Code Node (Filter) → Næste processing node  
Blacklist Node ↗
```

## Tilpasning af blacklist

### I den simple version:
Rediger `BLACKLIST` array direkte i `n8n_rss_filter.js`

### I den fleksible version:
- Opdater `blacklist.json` filen
- Eller send blacklist data fra en anden node

## Test

Du kan teste filteret ved at køre:
```bash
python test_filter.py
```

Dette vil vise dig hvilke items der bliver filtreret ud.

## Fejlfinding

- Tjek n8n's console output for log beskeder
- Scriptet logger hvilke items der bliver filtreret ud
- Hvis ingen items kommer igennem, tjek at input formatet matcher forventet struktur
